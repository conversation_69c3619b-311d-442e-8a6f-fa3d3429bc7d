# 🔨 打地鼠游戏

一个有趣的网页版打地鼠游戏，使用纯HTML、CSS和JavaScript开发。

## 🎮 游戏特色

- **响应式设计**：适配各种屏幕尺寸
- **等级系统**：随着分数增加，游戏难度逐渐提升
- **动画效果**：流畅的地鼠出现和击中动画
- **音效支持**：击中地鼠时的音效反馈
- **得分显示**：实时显示得分、时间和等级

## 🎯 游戏规则

1. 点击"开始游戏"按钮开始游戏
2. 游戏时间为30秒
3. 地鼠会随机从9个洞中出现
4. 点击地鼠可以获得分数
5. 每100分升一级，游戏难度增加
6. 时间结束后显示最终得分和等级

## 🎲 游戏机制

- **基础分数**：每击中一只地鼠获得 `等级 × 10` 分
- **等级提升**：每100分升一级
- **难度递增**：
  - 地鼠出现间隔随等级减少
  - 地鼠显示时间随等级缩短
  - 最高难度：地鼠每800ms出现一次，显示600ms

## 🚀 如何运行

1. 确保所有文件在同一目录下：
   - `index.html`
   - `style.css`
   - `script.js`

2. 用浏览器打开 `index.html` 文件

3. 点击"开始游戏"开始玩耍！

## 🎨 技术特点

- **纯前端实现**：无需服务器，直接在浏览器中运行
- **现代CSS**：使用Grid布局、渐变背景、动画效果
- **面向对象JavaScript**：清晰的代码结构，易于维护和扩展
- **Web Audio API**：原生音效支持
- **移动端友好**：响应式设计，支持触摸操作

## 🎵 音效说明

游戏使用Web Audio API生成击中音效，如果浏览器不支持，游戏仍可正常运行，只是没有音效。

## 🔧 自定义选项

你可以通过修改 `script.js` 中的参数来调整游戏：

- `timeLeft`：游戏时长（秒）
- `moleSpeed`：初始地鼠出现间隔（毫秒）
- `moleShowTime`：初始地鼠显示时间（毫秒）

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 🎉 享受游戏！

快来挑战你的反应速度，看看你能达到多少分！
