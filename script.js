class WhackAMoleGame {
    constructor() {
        this.score = 0;
        this.timeLeft = 30;
        this.level = 1;
        this.isGameRunning = false;
        this.gameTimer = null;
        this.moleTimer = null;
        this.moleSpeed = 1500; // 地鼠出现间隔（毫秒）
        this.moleShowTime = 1000; // 地鼠显示时间（毫秒）
        
        this.initElements();
        this.bindEvents();
    }

    initElements() {
        this.scoreElement = document.getElementById('score');
        this.timeElement = document.getElementById('time');
        this.levelElement = document.getElementById('level');
        this.startBtn = document.getElementById('startBtn');
        this.gameOverElement = document.getElementById('gameOver');
        this.finalScoreElement = document.getElementById('finalScore');
        this.finalLevelElement = document.getElementById('finalLevel');
        this.restartBtn = document.getElementById('restartBtn');
        this.holes = document.querySelectorAll('.hole');
        this.moles = document.querySelectorAll('.mole');
    }

    bindEvents() {
        this.startBtn.addEventListener('click', () => this.startGame());
        this.restartBtn.addEventListener('click', () => this.restartGame());
        
        this.moles.forEach((mole, index) => {
            mole.addEventListener('click', () => this.hitMole(index));
        });
    }

    startGame() {
        this.isGameRunning = true;
        this.score = 0;
        this.timeLeft = 30;
        this.level = 1;
        this.moleSpeed = 1500;
        this.moleShowTime = 1000;
        
        this.updateDisplay();
        this.startBtn.style.display = 'none';
        
        // 开始计时器
        this.gameTimer = setInterval(() => {
            this.timeLeft--;
            this.updateDisplay();
            
            if (this.timeLeft <= 0) {
                this.endGame();
            }
        }, 1000);
        
        // 开始生成地鼠
        this.generateMole();
    }

    generateMole() {
        if (!this.isGameRunning) return;
        
        // 随机选择一个洞
        const randomHole = Math.floor(Math.random() * this.holes.length);
        const mole = this.moles[randomHole];
        
        // 确保地鼠不在同一个洞连续出现
        if (mole.classList.contains('show')) {
            this.generateMole();
            return;
        }
        
        // 显示地鼠
        mole.classList.add('show');
        
        // 设置地鼠消失时间
        setTimeout(() => {
            mole.classList.remove('show');
        }, this.moleShowTime);
        
        // 设置下一个地鼠出现时间
        this.moleTimer = setTimeout(() => {
            this.generateMole();
        }, this.moleSpeed);
    }

    hitMole(index) {
        const mole = this.moles[index];
        
        if (!mole.classList.contains('show') || !this.isGameRunning) {
            return;
        }
        
        // 地鼠被击中
        mole.classList.remove('show');
        mole.classList.add('hit');
        
        // 增加分数
        const points = this.level * 10;
        this.score += points;
        
        // 显示得分动画
        this.showScorePopup(this.holes[index], points);
        
        // 移除击中动画
        setTimeout(() => {
            mole.classList.remove('hit');
        }, 300);
        
        // 检查是否升级
        this.checkLevelUp();
        
        this.updateDisplay();
    }

    showScorePopup(hole, points) {
        const popup = document.createElement('div');
        popup.className = 'score-popup';
        popup.textContent = `+${points}`;
        
        const rect = hole.getBoundingClientRect();
        popup.style.position = 'fixed';
        popup.style.left = rect.left + rect.width / 2 + 'px';
        popup.style.top = rect.top + 'px';
        popup.style.transform = 'translateX(-50%)';
        
        document.body.appendChild(popup);
        
        setTimeout(() => {
            document.body.removeChild(popup);
        }, 1000);
    }

    checkLevelUp() {
        const newLevel = Math.floor(this.score / 100) + 1;
        if (newLevel > this.level) {
            this.level = newLevel;
            // 增加难度：减少地鼠出现间隔和显示时间
            this.moleSpeed = Math.max(800, 1500 - (this.level - 1) * 100);
            this.moleShowTime = Math.max(600, 1000 - (this.level - 1) * 50);
        }
    }

    updateDisplay() {
        this.scoreElement.textContent = this.score;
        this.timeElement.textContent = this.timeLeft;
        this.levelElement.textContent = this.level;
    }

    endGame() {
        this.isGameRunning = false;
        
        // 清除计时器
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
        }
        if (this.moleTimer) {
            clearTimeout(this.moleTimer);
        }
        
        // 隐藏所有地鼠
        this.moles.forEach(mole => {
            mole.classList.remove('show');
        });
        
        // 显示游戏结束界面
        this.finalScoreElement.textContent = this.score;
        this.finalLevelElement.textContent = this.level;
        this.gameOverElement.style.display = 'flex';
    }

    restartGame() {
        this.gameOverElement.style.display = 'none';
        this.startBtn.style.display = 'inline-block';
        
        // 重置所有地鼠状态
        this.moles.forEach(mole => {
            mole.classList.remove('show', 'hit');
        });
    }
}

// 游戏音效（可选）
class GameSounds {
    constructor() {
        this.audioContext = null;
        this.initAudio();
    }

    initAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
        }
    }

    playHitSound() {
        if (!this.audioContext) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const game = new WhackAMoleGame();
    const sounds = new GameSounds();
    
    // 添加音效到击中事件
    const originalHitMole = game.hitMole.bind(game);
    game.hitMole = function(index) {
        const result = originalHitMole(index);
        if (this.moles[index].classList.contains('show')) {
            sounds.playHitSound();
        }
        return result;
    };
});

// 添加键盘支持
document.addEventListener('keydown', (e) => {
    if (e.code === 'Space') {
        e.preventDefault();
        const startBtn = document.getElementById('startBtn');
        if (startBtn.style.display !== 'none') {
            startBtn.click();
        }
    }
});
