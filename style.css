* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    user-select: none;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 100%;
}

.game-header {
    text-align: center;
    margin-bottom: 30px;
}

.game-header h1 {
    color: #333;
    font-size: 2.5em;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    font-size: 1.2em;
    font-weight: bold;
    color: #555;
}

.score-container, .time-container, .level-container {
    background: #f0f0f0;
    padding: 10px 15px;
    border-radius: 10px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.start-btn, .restart-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1em;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(238, 90, 36, 0.4);
}

.start-btn:hover, .restart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(238, 90, 36, 0.6);
}

.start-btn:active, .restart-btn:active {
    transform: translateY(0);
}

.game-board {
    margin: 30px 0;
}

.holes-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    max-width: 450px;
    margin: 0 auto;
}

.hole {
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, #8b4513 0%, #654321 70%, #3e2723 100%);
    border-radius: 50%;
    position: relative;
    cursor: pointer;
    box-shadow: inset 0 8px 16px rgba(0, 0, 0, 0.6);
    overflow: hidden;
    transition: all 0.2s ease;
}

.hole:hover {
    transform: scale(1.05);
}

.mole {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #8b4513, #a0522d);
    border-radius: 50% 50% 40% 40%;
    position: absolute;
    bottom: -80px;
    left: 50%;
    transform: translateX(-50%);
    transition: bottom 0.3s ease;
    cursor: pointer;
}

.mole::before {
    content: '🐹';
    font-size: 2.5em;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.mole.show {
    bottom: 10px;
    animation: pop 0.3s ease;
}

.mole.hit {
    animation: hit 0.3s ease;
}

@keyframes pop {
    0% { bottom: -80px; }
    50% { bottom: 15px; }
    100% { bottom: 10px; }
}

@keyframes hit {
    0% { transform: translateX(-50%) scale(1); }
    50% { transform: translateX(-50%) scale(1.2); }
    100% { transform: translateX(-50%) scale(0); }
}

.game-over {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.game-over-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.game-over-content h2 {
    color: #333;
    font-size: 2em;
    margin-bottom: 20px;
}

.game-over-content p {
    font-size: 1.2em;
    margin-bottom: 15px;
    color: #666;
}

.score-popup {
    position: absolute;
    color: #ff6b6b;
    font-weight: bold;
    font-size: 1.5em;
    pointer-events: none;
    animation: scoreFloat 1s ease-out forwards;
}

@keyframes scoreFloat {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-50px);
    }
}

@media (max-width: 600px) {
    .game-container {
        margin: 20px;
        padding: 20px;
    }
    
    .holes-grid {
        gap: 15px;
    }
    
    .hole {
        width: 90px;
        height: 90px;
    }
    
    .mole {
        width: 60px;
        height: 60px;
    }
    
    .mole::before {
        font-size: 2em;
    }
}
